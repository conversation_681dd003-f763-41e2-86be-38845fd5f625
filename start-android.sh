npm run build

export JAVA_HOME=/Library/Java/JavaVirtualMachines/temurin-11.jdk/Contents/Home && cordova build android -- --gradleArg=-PcdvBuildToolsVersion=35.0.1

$ANDROID_HOME/platform-tools/adb -s emulator-5554 install -r /Users/<USER>/Documents/Development/VisualCodeProjects/Angular/nustar-work-permit/platforms/android/app/build/outputs/apk/debug/app-debug.apk

$ANDROID_HOME/platform-tools/adb -s emulator-5554 shell am start -n com.unvired.permit/.MainActivity

adb logcat | grep -E "(App initialization | Android detected - checking permissions | showPermissionsAlert called | Permissions needed: | Navigating to permissions page | No permissions needed, initializing login process | ngoninit called in app component)"