import { Component, OnInit } from '@angular/core';
import { Platform } from '@ionic/angular';
import { Router } from '@angular/router';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { AndroidPermissions } from '@awesome-cordova-plugins/android-permissions/ngx';
import { DataService } from 'src/app/services/data.service';
import { AppConstants } from 'src/app/shared/app-constants';

@Component({
  selector: 'app-startup',
  templateUrl: './startup.page.html',
  styleUrls: ['./startup.page.scss'],
})
export class StartupPage implements OnInit {
  private constants: AppConstants;
  private permissions: Array<string> = [];

  constructor(
    private platform: Platform,
    private router: Router,
    private device: Device,
    private androidPermissions: AndroidPermissions,
    private dataService: DataService
  ) {
    this.constants = new AppConstants();
  }

  async ngOnInit() {
    // Wait for platform to be ready
    await this.platform.ready();
    
    // Perform platform-specific routing
    await this.handlePlatformSpecificRouting();
  }

  private async handlePlatformSpecificRouting() {
    try {
      if (this.platform.is('android')) {
        // Android: Check permissions first
        await this.checkAndroidPermissions();
      } else {
        // Browser or other platforms: Set initialized flag and go to regular login
        this.dataService.setInitialized(true);
        await this.dataService.initializeLoginProcess();
      }
    } catch (error) {
      console.error('Error in platform-specific routing:', error);
      // Fallback to login page
      this.router.navigate(['/login']);
    }
  }

  private async checkAndroidPermissions() {
    this.permissions = [];
    
    // Check permissions based on Android version
    if (parseInt(this.device.version) > 12) {
      let storageReadMediaImages = await this.androidPermissions.checkPermission("android.permission.READ_MEDIA_IMAGES");
      if (storageReadMediaImages.hasPermission == false) {
        this.permissions.push(this.constants.REQUIRED_PERMISSION.STORAGE);
      }

      let pushNotif = await this.androidPermissions.checkPermission("android.permission.POST_NOTIFICATIONS");
      if (pushNotif.hasPermission == false) {
        this.permissions.push(this.constants.REQUIRED_PERMISSION.NOTIFICATION);
      }
    } else {
      let storageRead = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_EXTERNAL_STORAGE);
      let storageWrite = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.WRITE_EXTERNAL_STORAGE);
      if (storageRead.hasPermission == false || storageWrite.hasPermission == false) {
        this.permissions.push(this.constants.REQUIRED_PERMISSION.STORAGE);
      }
    }

    let phoneState = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.READ_PHONE_STATE);
    if (phoneState.hasPermission == false) {
      this.permissions.push(this.constants.REQUIRED_PERMISSION.PHONE_STATE);
    }

    let cameraPermission = await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.CAMERA);
    if (cameraPermission.hasPermission == false) {
      this.permissions.push(this.constants.REQUIRED_PERMISSION.CAMERA);
    }

    await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_FINE_LOCATION);
    await this.androidPermissions.checkPermission(this.androidPermissions.PERMISSION.ACCESS_COARSE_LOCATION);

    // Navigate based on permissions
    if (this.permissions.length > 0) {
      this.router.navigate(['/permissions']);
    } else {
      // Set initialized flag before calling initializeLoginProcess
      this.dataService.setInitialized(true);
      await this.dataService.initializeLoginProcess();
    }
  }
}
