import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { InitGuard } from './guards/init.guard';

const routes: Routes = [
  {
    path: '',
    redirectTo: 'startup',
    pathMatch: 'full'
  },
  {
    path: 'startup',
    loadChildren: () => import('./pages/startup/startup.module').then(m => m.StartupPageModule)
  },
  {
    path: 'login',
    loadChildren: () =>
      import('./pages/login/login.module').then((m) => m.LoginPageModule),
  },
  {
    path: 'contractor-login',
    loadChildren: () =>
      import('./pages/contractor-login/contractor-login.module').then(
        (m) => m.ContractorLoginPageModule
      ),
  },
  {
    path: 'settings',
    loadChildren: () =>
      import('./pages/settings/settings.module').then(
        (m) => m.SettingsPageModule
      ),
  },
  {
    path: 'master-data',
    loadChildren: () =>
      import('./pages/master-data/master-data.module').then(
        (m) => m.MasterDataPageModule
      ),
  },
  {
    path: 'users',
    loadChildren: () =>
      import('./pages/users/users.module').then((m) => m.UsersPageModule),
  },
  {
    path: 'agents',
    loadChildren: () =>
      import('./pages/agents/agents.module').then((m) => m.AgentsPageModule),
  },
  {
    path: 'facilities-divisions',
    loadChildren: () =>
      import('./pages/facilities-divisions/facilities-divisions.module').then(
        (m) => m.FacilitiesDivisionsPageModule
      ),
  },
  {
    path: 'structure',
    loadChildren: () => import('./pages/structure/structure.module').then(m => m.StructurePageModule)
  },
  {
    path: 'permits',
    loadChildren: () =>
      import('./pages/permits/permits.module').then((m) => m.PermitsPageModule),
    canActivate: [InitGuard]
  },
  {
    path: 'form-render',
    loadChildren: () =>
      import('./pages/form-render/form-render.module').then(
        (m) => m.FormRenderPageModule
      ),
  },
  {
    path: 'options-popover',
    loadChildren: () =>
      import('./pages/options-popover/options-popover.module').then(
        (m) => m.OptionsPopoverPageModule
      ),
  },
  {
    path: 'camera',
    loadChildren: () =>
      import('./pages/camera/camera.module').then((m) => m.CameraPageModule),
  },
  {
    path: 'image-preview',
    loadChildren: () => import('./pages/image-preview/image-preview.module').then( m => m.ImagePreviewPageModule)
  },
  {
    path: 'reports',
    loadChildren: () => import('./pages/reports/reports.module').then( m => m.ReportsPageModule)
  },
  {
    path: 'ssologin',
    loadChildren: () => import('./pages/saml-sso/saml-sso.module').then( m => m.SamlSsoPageModule)
  },
  {
    path: 'mobile-login',
    loadChildren: () => import('./pages/mobile-login/mobile-login.module').then( m => m.MobileLoginPageModule)
  },
  {
    path: 'mobile-home',
    loadChildren: () => import('./pages/mobile-home/mobile-home.module').then( m => m.MobileHomePageModule)
  },
  {
    path: 'permissions',
    loadChildren: () => import('./pages/permissions/permissions.module').then( m => m.PermissionsPageModule)
  }
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
